import { useState, useEffect, useMemo } from 'react';
import { Country } from '@/types';

// Simuler un chargement lazy des pays
const loadCountries = async (): Promise<Country[]> => {
  // Simuler un délai de chargement
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Importer dynamiquement les pays
  const { COUNTRIES } = await import('@/constants/countries');
  return COUNTRIES;
};

export const useCountries = (searchTerm: string = '') => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await loadCountries();
        setCountries(data);
      } catch (err) {
        setError('Erreur lors du chargement des pays');
        console.error('Error loading countries:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Filtrer et trier les pays en fonction du terme de recherche
  const filteredCountries = useMemo(() => {
    if (!countries.length) return [];
    
    let filtered = countries;
    
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = countries.filter(country =>
        country.name.common.toLowerCase().includes(searchLower) ||
        country.name.official.toLowerCase().includes(searchLower)
      );
    }
    
    // Trier par nom commun
    return filtered.sort((a, b) => 
      a.name.common.localeCompare(b.name.common)
    );
  }, [countries, searchTerm]);

  return {
    countries: filteredCountries,
    isLoading,
    error,
    totalCount: countries.length
  };
};
