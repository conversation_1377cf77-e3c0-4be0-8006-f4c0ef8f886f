{"name": "tcp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": " next start", "lint": "next lint", "lint:fix": "eslint . --fix", "format": "prettier --check .", "format:fix": "prettier --write --list-different .", "analyze": "cross-env ANALYZE=true next build", "prepare": "husky", "lint-staged": "lint-staged"}, "dependencies": {"@floating-ui/react": "^0.27.9", "@hookform/resolvers": "^3.3.2", "@mantine/hooks": "^6.0.20", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "1.0.4", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-hover-card": "^1.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-tooltip": "^1.0.6", "@react-pdf/renderer": "^4.3.0", "@tanstack/query-core": "^5.51.21", "@tanstack/react-query": "^4.32.6", "@tanstack/react-table": "^8.9.3", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.5.6", "@types/node": "20.4.5", "@types/react": "18.2.17", "@types/react-dom": "18.2.7", "@vercel/analytics": "^1.0.1", "@vercel/speed-insights": "^1.0.1", "accept-language": "^3.0.18", "animate.css": "^4.1.1", "autoprefixer": "^10.4.20", "axios": "^1.4.0", "class-variance-authority": "^0.7.0", "client-only": "^0.0.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "copy-to-clipboard": "^3.3.3", "date-fns": "^2.30.0", "eslint": "8.45.0", "eslint-config-next": "^14.2.3", "framer-motion": "^10.16.4", "html-react-parser": "^5.1.10", "html-to-text": "^9.0.5", "i18next": "^23.4.6", "i18next-browser-languagedetector": "^7.1.0", "i18next-resources-to-backend": "^1.1.4", "javascript-time-ago": "^2.5.10", "lucide-react": "^0.511.0", "next": "^14.2.3", "next-auth": "^4.24.11", "nextjs-toploader": "^1.6.12", "nuqs": "^2.2.3", "postcss": "^8.4.40", "react": "^18.3.1", "react-audio-visualize": "^1.2.0", "react-cookie": "^6.1.1", "react-cookie-consent": "^9.0.0", "react-dom": "^18.3.1", "react-h5-audio-player": "^3.9.1", "react-hook-form": "^7.47.0", "react-i18next": "^13.2.1", "react-icons": "^4.10.1", "react-indexed-db": "^2.0.1", "react-indexed-db-hook": "^1.0.14", "react-intersection-observer": "^9.8.2", "react-loading-skeleton": "^3.3.1", "react-number-format": "^5.3.1", "react-scroll-to-top": "^3.0.0", "react-sweet-progress": "^1.1.2", "react-time-ago": "^7.2.1", "react-timer-hook": "^3.0.7", "react-to-pdf": "^1.0.1", "react-voice-visualizer": "^1.0.12", "resend": "^1.1.0", "server-only": "^0.0.1", "sharp": "^0.33.4", "sonner": "^1.4.41", "styled-components": "^6.0.7", "swiper": "^11.0.5", "tailwind-merge": "^1.14.0", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.6", "ts-custom-error": "^3.3.1", "typescript": "5.1.6", "use-sound": "^4.0.1", "uuid": "^9.0.1", "vaul": "^0.9.1", "word-count": "^0.2.2", "zod": "^3.23.8", "zsa": "^0.5.1", "zsa-react": "^0.2.2", "zustand": "^4.3.9"}, "overrides": {}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@next/bundle-analyzer": "^14.2.5", "@tailwindcss/typography": "^0.5.10", "@tiptap/cli": "^1.1.12", "@types/howler": "^2.2.11", "@types/uuid": "^9.0.4", "conventional-changelog-atom": "^5.0.0", "cross-env": "^7.0.3", "eslint-config-prettier": "10.1.5", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "sass": "^1.89.1", "tailwind-clip-path": "^1.0.0", "webpack-bundle-analyzer": "^4.10.2"}}