import API from '@/lib/axios';
import { Serie } from '@/types';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
interface ResultNewsTCF {
  result: Serie[];
  currentPage: number;
  maxPage: number;
}
interface ResultNewsTEF {
  result: SerieTEF[];
  currentPage: number;
  maxPage: number;
}
const fetchItemsTCF = async ({ pageParam = 1 }): Promise<ResultNewsTCF> => {
  const { data } = await API.get<ResultNewsTCF>(
    `/api/serie/series/expressionQuestions?page=${pageParam}`,
  );
  return data;
};

export const useFetchTCFNews = () => {
  const { data, fetchNextPage, status, isFetchingNextPage, error } =
    useInfiniteQuery({
      queryKey: ['news'],
      queryFn: fetchItemsTCF,
      getNextPageParam: (lastPage, allPages) => {
        return lastPage.maxPage >= 5 ? lastPage.currentPage : undefined;
      },
    });

  return {
    data,
    fetchNextPage,
    status,
    isFetchingNextPage,
    error,
  } as const;
};

const fetchItemsTEF = async ({ pageParam = 1 }): Promise<ResultNewsTEF> => {
  const { data } = await API.get<ResultNewsTEF>(
    `/api/tef/serie/series/expressionQuestions?page=${pageParam}`,
  );
  console.log(data);

  return data;
};

export const useFetchTEFNews = () => {
  const { data, fetchNextPage, status, isFetchingNextPage, error } =
    useInfiniteQuery({
      queryKey: ['news'],
      queryFn: fetchItemsTEF,
      getNextPageParam: (lastPage, allPages) => {
        return lastPage.maxPage >= 5 ? lastPage.currentPage : undefined;
      },
    });

  return {
    data,
    fetchNextPage,
    status,
    isFetchingNextPage,
    error,
  } as const;
};

interface UseRefetchInScrollProps {
  onScroll: () => void;
}
export const useRefetchInScroll = ({ onScroll }: UseRefetchInScrollProps) => {
  const { ref, inView } = useInView();
  useEffect(() => {
    if (inView) {
      onScroll();
    }
  }, [inView]);

  return { ref } as const;
};
