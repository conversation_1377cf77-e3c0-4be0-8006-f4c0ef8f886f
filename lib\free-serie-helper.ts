import { BASEURL } from '@/config';
import { AxiosError } from 'axios';

export const getFreeSerrie = async (type: 'tef' | 'tcf') => {
  try {
    const response = await fetch(`${BASEURL}/api/free_serie/${type}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
      },
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    if (error instanceof AxiosError) {
      if (error.response?.status === 400) {
        return null;
      }
      return null;
    }
    return null;
  }
};
