import axios from 'axios';
import { getAuthSession } from './auth';
import { redirect } from 'next/navigation';
import { NotNetworkError, TimeOutError } from '@/types/error';
import { BASEURL } from '@/config';
const SERVER_API = axios.create({
  baseURL: BASEURL,
  // timeout: 20000
});

SERVER_API.interceptors.request.use(async (config) => {
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';

  config.headers.Authorization = `Bearer ${token}`;
  config.headers['Content-Type'] = 'application/json';
  config.headers.Accept = 'application/json';
  return config;
});

SERVER_API.interceptors.response.use(
  function (response) {
    return response;
  },
  function (error) {
    console.log(error);

    if (!error.response) {
      console.error(
        'Erreur de réseau: Veuillez vérifier votre connexion Internet.',
        error,
      );
      return Promise.reject(new NotNetworkError());
    } else if (error.code === 'ECONNABORTED') {
      console.error('La requête a expiré. Veuillez réessayer.', error);
      return Promise.reject(new TimeOutError());
    }
    if (
      (error.response && error.response.status === 401) ||
      error.response.status == 403
    ) {
      const message = (error.response.data as { message: string }).message;
      console.log(error);

      if (message === 'Invalid or Expired Token provided !') {
        console.log('redirect from axios instance');
        redirect('/auth');
      }
    }
    return Promise.reject(error);
  },
);

export default SERVER_API;

// Fonction pour créer une instance d'Axios avec des headers personnalisés
export const createAxiosInstance = (headers: any) => {
  const instance = axios.create({
    baseURL: BASEURL,
    timeout: 14000,
    headers: {
      ...headers,
      Accept: 'appliction/json',
      'Content-Type': 'application/json',
    },
  });

  instance.interceptors.response.use(
    function (response) {
      return response;
    },
    function (error) {
      if (!error.response) {
        console.error(
          'Erreur de réseau: Veuillez vérifier votre connexion Internet.',
          error,
        );
        return Promise.reject(new NotNetworkError());
      } else if (error.code === 'ECONNABORTED') {
        console.error('La requête a expiré. Veuillez réessayer.', error);
        return Promise.reject(new TimeOutError());
      }
      if (
        (error.response && error.response.status === 401) ||
        error.response.status == 403
      ) {
        const message = (error.response.data as { message: string }).message;
        console.log(error);

        if (message === 'Invalid or Expired Token provided !') {
          redirect('/auth');
        }
      }
      return Promise.reject(error);
    },
  );
  return instance;
};
