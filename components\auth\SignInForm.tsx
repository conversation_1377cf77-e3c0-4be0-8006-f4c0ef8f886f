'use client';

import React, { useState } from 'react';
import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { Icons } from '../Icons';
import { signIn } from 'next-auth/react';
import { toast } from '../ui/use-toast';
import { useForm, SubmitHandler } from 'react-hook-form';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { UIInput } from '../ui/ac-input';
type SiginInput = {
  email: string;
  password: string;
};

export default function AuthenticationPage({
  callbackUrl,
}: {
  callbackUrl: string;
}) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SiginInput>({
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit: SubmitHandler<SiginInput> = async (
    { email, password },
    e,
  ) => {
    e?.preventDefault();
    setIsLoading(true);

    const res = await signIn('credentials', {
      email: email.trim(),
      password,
      redirect: false,
    });
    if (!res?.error) {
      signIn('credentials', {
        email,
        password,
        redirect: true,
        callbackUrl: callbackUrl || '/',
      }).then(() => {
        setIsLoading(false);
        toast({
          title: 'Connexion reussie',
        });
      });
    } else {
      setIsLoading(false);
      toast({
        title: 'Erreur',
        description: res?.error,
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      <div className="mx-auto flex w-full flex-col justify-center">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-4xl font-semibold tracking-tight">
            Bon retour !
          </h1>
          <p className="text-md text-muted-foreground">
            Connectez-vous pour continuer votre préparation et voir vos
            résultats...
          </p>
        </div>
        <div className={'grid gap-2 p-4 md:p-6'}>
          <form className="" onSubmit={handleSubmit(onSubmit)} noValidate>
            <div className="">
              <LabelInputContainer className="mb-4">
                <Label className="" htmlFor="email">
                  Email
                </Label>
                <UIInput
                  id="email"
                  tabIndex={1}
                  placeholder="<EMAIL>"
                  type="email"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  disabled={isLoading}
                  {...register('email', {
                    required: {
                      value: true,
                      message: 'Ce champ est obligatoire',
                    },
                    validate: {
                      isValidEmail: (fieldValue: string) => {
                        const emailPart = fieldValue.split('/')[0]; // Prend uniquement la partie avant le slash
                        const reg =
                          /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

                        return reg.test(emailPart) || 'Adresse mail non valide';
                      },
                    },
                  })}
                />
                {errors.email && (
                  <p className="my-1 text-sm text-red-500">
                    {errors.email.message}
                  </p>
                )}
              </LabelInputContainer>

              <LabelInputContainer className="mb-4">
                <Label className="" htmlFor="pwd">
                  Mot de passe
                </Label>
                <UIInput
                  id="pwd"
                  placeholder="••••••••"
                  type="password"
                  tabIndex={2}
                  autoCapitalize="none"
                  autoCorrect="off"
                  {...register('password', {
                    required: {
                      value: true,
                      message: 'Ce champs est obligatoire',
                    },
                  })}
                  disabled={isLoading}
                />
                {errors.password && (
                  <p className="my-1 text-sm text-red-500">
                    {errors.password.message}
                  </p>
                )}
              </LabelInputContainer>

              <Button
                tabIndex={3}
                disabled={isLoading}
                className="group w-full"
              >
                {isLoading ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <>
                    SE CONNECTER{' '}
                    <span className="transition-all duration-300 group-hover:ml-2">
                      &rarr;
                    </span>
                  </>
                )}

                <BottomGradient />
              </Button>
            </div>
          </form>
          <Button
            onClick={() => {
              router.replace('/reset-password');
            }}
            variant={'link'}
            tabIndex={4}
            disabled={isLoading}
            className="justify-end"
          >
            Mot de passe oublié ?
          </Button>
        </div>

        <p className="flex px-8 text-center text-sm text-muted-foreground">
          Vous n&apos;avez pas de compte?{' '}
          <Link
            href={`/signup?callbackUrl=${callbackUrl ?? '/'}`}
            replace
            className="text-md pl-0.5 text-blue-500 underline underline-offset-4"
          >
            S&apos;inscrire
          </Link>
          .
        </p>
      </div>
    </>
  );
}

const BottomGradient = () => {
  return (
    <>
      <span className="absolute inset-x-0 -bottom-px block h-px w-full bg-gradient-to-r from-transparent via-cyan-500 to-transparent opacity-0 transition duration-500 group-hover/btn:opacity-100" />
      <span className="absolute inset-x-10 -bottom-px mx-auto block h-px w-1/2 bg-gradient-to-r from-transparent via-indigo-500 to-transparent opacity-0 blur-sm transition duration-500 group-hover/btn:opacity-100" />
    </>
  );
};

const LabelInputContainer = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn('flex w-full flex-col space-y-2', className)}>
      {children}
    </div>
  );
};
