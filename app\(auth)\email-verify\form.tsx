'use client';
import { Button, buttonVariants } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useSession } from 'next-auth/react';
import React, { useState, useTransition } from 'react';
import { sendEmailVerificationCode, verifyEmailCode } from '../actions';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { parseAsBoolean, useQueryState } from 'nuqs';

function EmailVerifyForm() {
  const { data: session, update: updateSession } = useSession();
  const [haveCode, setHaveCode] = useQueryState(
    'haveCode',
    parseAsBoolean.withDefault(false),
  );
  const [email, setEmail] = useState(session?.user.email);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const submitSendEmailOTPAction = async () => {
    startTransition(async () => {
      const data = email || session?.user.email!;
      const { error, email: resultEmail } =
        await sendEmailVerificationCode(data);
      if (error) {
        toast.error(error);
      } else {
        if (data !== resultEmail) {
          updateSession({ ...session?.user, email: resultEmail });
          toast.success('Email modifié avec succès et code envoyé');
        } else {
          toast.success('Code envoyé avec succès');
        }
        setEmail(resultEmail);
      }
    });
    setHaveCode(true);
  };

  const submitActiveEmailOTPAction = async (data: FormData) => {
    startTransition(async () => {
      const otp = data.get('code') as string;
      const { error, user } = await verifyEmailCode(
        email || session?.user.email!,
        otp,
      );
      if (error) {
        toast.error(error);
      } else {
        updateSession(user);
        toast.success('Email vérifié avec succès');
        router.replace('/');
      }
    });
  };
  return (
    <>
      {!haveCode ? (
        <>
          <span className="max-w-[50ch] text-center">
            Par defaut nous utiliserons l&apos;email de votre compte{' '}
            <p className="inline font-bold">({session?.user.email})</p>, mais
            vous pouvez le modifier dans le formulaire ci-dessous.
          </span>
          <form
            action={submitSendEmailOTPAction}
            className="mt-5 grid w-full gap-3"
          >
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                value={email ?? ''}
                placeholder={session?.user.email || ''}
                onChange={(e) => setEmail(e.target.value)}
                type="email"
              />
            </div>
            <Button type="submit" disabled={isPending}>
              Envoyer le code
            </Button>
            <div className="flex justify-end">
              <span
                className={buttonVariants({
                  variant: 'link',
                  className: `${isPending ? '' : ''}`,
                })}
                onClick={() => {
                  if (!isPending) {
                    if (!email) {
                      toast.warning('Veuillez entrer un email valide');
                    } else {
                      setHaveCode(true);
                    }
                  }
                }}
              >
                {' '}
                J&apos;ai déja un code
              </span>
            </div>
          </form>
        </>
      ) : (
        <>
          <span className="max-w-[50ch] text-center">
            Entrer le code à 4 chiffres que nous vous avons envoyé par email à :{' '}
            <p className="inline font-bold">({email || session?.user.email})</p>
          </span>
          <form
            action={submitActiveEmailOTPAction}
            className="mt-5 grid w-full gap-3"
          >
            <div className="grid gap-2">
              <Label htmlFor="code">Code de verification</Label>
              <Input
                required
                type="number"
                name="code"
                id="code"
                disabled={isPending}
              />
            </div>
            <Button type="submit" disabled={isPending}>
              Verifier
            </Button>
            <div className="flex justify-end">
              <span
                className={buttonVariants({
                  variant: 'link',
                  className: `${isPending ? '' : ''}`,
                })}
                onClick={() => {
                  if (isPending) return;
                  setHaveCode(false);
                }}
              >
                Je n&apos;ai pas reçu de code
              </span>
            </div>
          </form>
        </>
      )}
    </>
  );
}

export default EmailVerifyForm;
