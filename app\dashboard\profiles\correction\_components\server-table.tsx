import { ExpressionTest, ExpressionTestResult } from '@/types';
import ExpressionList from './list';
import { Skeleton } from '@/components/ui/skeleton';
import axios from 'axios';
import { getAuthSession } from '@/lib/auth';
const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
async function getTestsEE(token: string): Promise<ExpressionTestResult[]> {
  const { data } = await axios.get<{ tests: ExpressionTest[] }>(
    `${baseUrl}/api/dealer/profiles/testsee-en-cours`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return data.tests.map((test) => ({ ...test, type: 'EE' }));
}

async function getTestsEO(token: string): Promise<ExpressionTestResult[]> {
  const { data } = await axios.get<{ tests: ExpressionTest[] }>(
    `${baseUrl}/api/dealer/profiles/testseo-en-cours`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return data.tests.map((test) => ({ ...test, type: 'EO' }));
}

export const WritingExpression = async () => {
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';
  const data = session?.user.role! == 'dealer' ? await getTestsEE(token) : [];
  return <ExpressionList data={data} />;
};

export const OralExpression = async () => {
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';
  const data = session?.user.role! == 'dealer' ? await getTestsEO(token) : [];
  return <ExpressionList data={data} />;
};

export const ExpressionLoader = () => {
  return <Skeleton className="h-[100px] w-full rounded-md" />;
};
