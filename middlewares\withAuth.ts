import { hasSupscription } from '@/lib/getDayCount';
import { getToken } from 'next-auth/jwt';
import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';

const getDayCount = (endDate: string | null) => {
  if (!endDate) return 0;
  const start = new Date(Date.now());
  const end = new Date(endDate);
  const difference = end.getTime() - start.getTime();
  const days = Math.ceil(difference / (1000 * 3600 * 24));
  return days > 0 ? days : 0;
};

export function isProfile(email: string | undefined | null): boolean {
  if (!email || typeof email !== 'string') return false;

  const parts = email.split('/');
  if (parts.length !== 2) return false;

  const [dealerEmail] = parts;

  // Expression régulière basique pour un email valide
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  return emailRegex.test(dealerEmail);
}

export function withAuth(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    if (!token) {
      const path = req.nextUrl.pathname;
      const search = req.nextUrl.search;
      if (!isAuthPath(req.nextUrl.pathname)) {
        return NextResponse.redirect(
          new URL(`/auth?callbackUrl=${path}${search}`, req.nextUrl),
        );
      } else {
        return middleware(req, event);
      }
    }

    if (isAuthPath(req.nextUrl.pathname)) {
      return NextResponse.redirect(new URL(`/`, req.nextUrl));
    }

    if (
      req.nextUrl.pathname.includes('email-verify') &&
      !hasSupscription(token)
    ) {
      return NextResponse.redirect(new URL(`/checkout`, req.nextUrl));
    }

    if (
      req.nextUrl.pathname.includes('/dashboard/profiles') &&
      isProfile(token.email)
    ) {
      return NextResponse.redirect(
        new URL(`/dashboard/tcfhistory`, req.nextUrl),
      );
    }

    if (req.nextUrl.pathname.includes('methodologie')) {
      const path = req.nextUrl.pathname;
      const search = req.nextUrl.search;
      let days: number = 0;
      if (req.nextUrl.pathname.includes('TCF')) {
        days =
          token?.role == 'admin'
            ? 100
            : getDayCount(token?.remains?.remainTCF?.remain_day || null);
      }
      if (req.nextUrl.pathname.includes('TEF')) {
        days =
          token?.role == 'admin'
            ? 100
            : getDayCount(token?.remains?.remainTEF?.remain_day || null);
      }

      if (days < 1) {
        return NextResponse.redirect(
          new URL(`/checkout?callbackUrl=${path}${search}`, req.nextUrl),
        );
      }
    }

    return middleware(req, event);
  };
}
function isAuthPath(pathname: string) {
  const AUTH_PATHS = ['/auth', '/signin', '/signup', '/reset-password'];
  return AUTH_PATHS.includes(pathname);
}
