'use client';
import { Input } from '@/components/ui/input';
import { z } from 'zod';
import { SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2 } from 'lucide-react';
import { AxiosError } from 'axios';
import { useSession } from 'next-auth/react';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { parseAsStringLiteral, useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';
import { EXAMS } from '@/config';
import { useCountry } from '@/hooks/use-country';
import { v4 as uuid } from 'uuid';
import API from '@/lib/axios';
import { toast as sonner } from 'sonner';
import { SubscriptionPack } from '@/types';

interface PhoneFormProps {
  buttonText?: string;
  offers: SubscriptionPack[];
  acceptPromo?: boolean;
}

export default function CardForm({
  buttonText,
  acceptPromo,
  offers,
}: PhoneFormProps) {
  const [recall, setRecall] = useState<boolean>(false);
  const { data: session } = useSession();
  const phoneSchema = z.object({
    parrain: z.string().optional(),
    acceptTerms: z.coerce.boolean().refine((value) => value, {
      message: 'Il faut accepter cette condition',
    }),
  });

  type PhoneFormValues = z.infer<typeof phoneSchema>;
  const form = useForm<PhoneFormValues>({
    mode: 'onChange',
    resolver: zodResolver(phoneSchema),
    defaultValues: {
      parrain: 'objectif',
    },
  });

  const onSubmit: SubmitHandler<PhoneFormValues> = async (values, e) => {
    e?.preventDefault();
    await initiatePayment();
  };

  const [examen] = useQueryState(
    'examen',
    parseAsStringLiteral(EXAMS).withDefault('TCF'),
  );

  const [selectedPrice] = useQueryState('selectedPrice');

  const { dealerContry } = useCountry();
  const pricing = offers?.find((np) => np._id == selectedPrice);

  const initiatePayment = async (phone?: string) => {
    try {
      // Rechercher le pricing correspondant
      const date = new Date(Date.now());
      const price = pricing?.[dealerContry]?.local || 0;

      // Génération de la référence unique
      const ref = `${uuid()}:${session?.user.email}:${date.toISOString()}`;

      // Création du payload
      const currency = 'EUR';

      const payload = {
        amount: price,
        currency,
        reason: `abonnement du ${new Date(Date.now()).toLocaleDateString()}  pour la préparation à l'examen du ${examen} au prix de ${price} ${currency}`,
        reference: ref,
        email: session?.user.email!,
        name: session?.user.email!,
        phone: phone ?? session?.user.phone,
        parrain: 'objectif',
        channel: `cm.card`,
        examen: examen,
        offre: pricing?._id,
      };

      console.log('Payload envoyé :', payload);

      // Appel de l'API
      const { data } = await API.post('/api/coolPayments/init-cardpayment', {
        ...payload,
      });

      if (data.result.status === 'success') {
        const paymentUrl = data.result.payment_url;
        window.location.href = paymentUrl; // Ouvre le paiement dans un nouvel onglet
      } else {
        console.error('Erreur dans la réponse :', data);
        sonner.error(
          "Une erreur est survenue lors de l'initialisation du paiement.",
        );
      }
    } catch (error) {
      console.error("Erreur lors de l'appel à l'API :", error);
      if (error instanceof AxiosError) {
        if (
          error.code === 'ERR_BAD_REQUEST' &&
          error.response?.data?.message ===
            '[customer_phone_number] : Invalid phone number !'
        ) {
          setRecall(true);
          return;
        }
      }
      sonner.error('Une erreur est survenue. Veuillez réessayer.');
    }
  };

  useEffect(() => {
    if (recall) {
      sonner.promise(initiatePayment('+237686876873'), {
        loading: 'Initialisation du paiement...',
      });
    }
  }, [recall]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid space-y-6">
        {acceptPromo ? (
          <FormField
            control={form.control}
            name="parrain"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="parrain">Code promo </FormLabel>
                <FormControl>
                  <Input
                    readOnly
                    id="parrain"
                    placeholder={`Entrez votre code promo`}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ) : null}

        <div className="items-top my-2 flex space-x-2">
          <div className="flex">
            <FormField
              control={form.control}
              name="acceptTerms"
              render={({ field }) => (
                <FormItem className="flex w-full gap-2">
                  <FormControl>
                    <Checkbox
                      id="terms1"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>

                  <div className="grid gap-1.5 text-left leading-none">
                    <label
                      htmlFor="terms1"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {"J'accepte les conditions."}
                      <sup className="text-lg font-bold text-red-500">*</sup>
                    </label>
                    <p className="text-sm text-muted-foreground">
                      En cochant cette case vous acceptez nos{' '}
                      <Link
                        href={'/condition-remboursement'}
                        className="underline underline-offset-4 hover:text-primary"
                      >
                        conditions de remboursement.
                      </Link>{' '}
                    </p>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          </div>
        </div>
        <Button
          disabled={
            form.formState.isSubmitting ||
            form.formState.isValidating ||
            !form.formState.isValid
          }
          type="submit"
          className="relative mt-3 w-full overflow-hidden"
        >
          <div
            className={`absolute inset-0 bg-primary ${
              form.formState.isSubmitting
                ? 'flex items-center justify-center'
                : 'hidden'
            }`}
          >
            <Loader2 className={`h-4 w-4 animate-spin`} />
          </div>
          {buttonText || 'Continuez'}
        </Button>
      </form>
    </Form>
  );
}
