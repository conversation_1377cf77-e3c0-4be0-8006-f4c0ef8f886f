'use client';
import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
} from '../ui/command';
import { ChevronsUpDown, Loader2 } from 'lucide-react';
import { useCountries } from '@/hooks/useCountries';
import { VirtualizedCountryList } from '../ui/VirtualizedCountryList';

export const CountrySelector: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState<string>('cameroon');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Utilisation du hook pour le chargement lazy des pays
  const { countries, isLoading: countriesLoading, error: countriesError } = useCountries(searchTerm);

  return (
    <div className="w-full max-w-md mx-auto p-4">
      <h2 className="text-lg font-semibold mb-4">Sélecteur de pays avec virtualisation</h2>
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {!countriesLoading && countries.length > 0 ? (
              <>
                {value
                  ? countries.find(
                      (pays) =>
                        pays.name.common.toLocaleLowerCase() ===
                        value.toLocaleLowerCase(),
                    )?.name.common || 'Pays sélectionné'
                  : 'Sélectionner un pays'}
              </>
            ) : (
              <span className="flex items-center">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Chargement...
              </span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          side="bottom"
          className="w-full max-w-md p-0"
        >
          <Command>
            <CommandInput 
              placeholder="Rechercher un pays..." 
              onValueChange={setSearchTerm}
            />
            <CommandEmpty>
              {countriesError ? 'Erreur de chargement' : 'Aucun pays trouvé'}
            </CommandEmpty>
            <CommandGroup>
              {countriesLoading ? (
                <div className="flex h-20 items-center justify-center">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2 text-sm">Chargement des pays...</span>
                </div>
              ) : (
                <VirtualizedCountryList
                  countries={countries}
                  selectedValue={value}
                  onSelect={(selectedValue) => {
                    setValue(
                      selectedValue === value.toLowerCase()
                        ? ''
                        : selectedValue
                    );
                    setOpen(false);
                  }}
                  height={320}
                />
              )}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Informations de débogage */}
      <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
        <p><strong>Pays sélectionné:</strong> {value || 'Aucun'}</p>
        <p><strong>Nombre de pays chargés:</strong> {countries.length}</p>
        <p><strong>Terme de recherche:</strong> {searchTerm || 'Aucun'}</p>
        <p><strong>État de chargement:</strong> {countriesLoading ? 'En cours...' : 'Terminé'}</p>
        {countriesError && (
          <p className="text-red-600"><strong>Erreur:</strong> {countriesError}</p>
        )}
      </div>
    </div>
  );
};
