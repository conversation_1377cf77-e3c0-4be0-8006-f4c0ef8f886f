import { User } from 'next-auth';

export const getDayCount = (endDate: string | null) => {
  if (!endDate) return 0;
  const start = new Date(Date.now());
  const end = new Date(endDate);
  const difference = end.getTime() - start.getTime();
  const days = Math.ceil(difference / (1000 * 3600 * 24));
  return days > 0 ? days : 0;
};

export const hasSupscription = (data: User) => {
  return (
    getDayCount(data.remains?.remainTCF?.remain_day || null) > 0 ||
    getDayCount(data.remains?.remainTEF?.remain_day || null) > 0
  );
};
