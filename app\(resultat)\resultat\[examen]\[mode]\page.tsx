'use client';
import DetailHeaderC from '@/components/DetailHeader';
import DisplayQuestion from '@/components/DisplayQuestion';
import 'react-sweet-progress/lib/style.css';

import ComprehensionResultHeaderTEF from '@/components/test/TEF/comprehension-result-header';
import {
  DisplayQuestionTEFCE,
  DisplayQuestionTEFCO,
} from '@/components/test/TEF/display-question-tef';
import { useOfflineState } from '@/context/tcf/anonymousSate';
import { notFound } from 'next/navigation';
type partialMode = 'CE' | 'CO';
type Examen = 'TEF' | 'TCF';

export default function Page({
  params,
}: {
  params: {
    examen: Examen;
    mode: partialMode;
  };
}) {
  const { detailSet } = useOfflineState();
  console.log(detailSet);

  if (!detailSet) {
    notFound();
  }

  return (
    <div
      className="flex h-fit w-[95vw] flex-col gap-5 px-5 py-10 md:w-full lg:px-28"
      suppressHydrationWarning
    >
      {params.examen == 'TCF' ? (
        <>
          <DetailHeaderC detailSet={detailSet} />
          <DisplayQuestion detailset={detailSet} />
        </>
      ) : (
        <>
          <ComprehensionResultHeaderTEF
            detailSet={detailSet as any}
            type={params.mode}
          />
          {params.mode == 'CE' && (
            <DisplayQuestionTEFCE detailset={detailSet as any} />
          )}
          {params.mode == 'CO' && (
            <DisplayQuestionTEFCO detailset={detailSet as any} />
          )}
        </>
      )}
    </div>
  );
}
