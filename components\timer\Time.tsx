/* eslint-disable react-hooks/exhaustive-deps */
'use client';
import { useTimer } from 'react-timer-hook';
import TimerStyled from './StyledTime';
import { useEffect } from 'react';
import {} from '@/lib/axios';
import { useSession } from 'next-auth/react';

interface Props {
  expiryTimestamp: Date;
  onExpire: () => void;
  autoStart?: boolean;
  starting?: boolean;
  mode?: 'CE' | 'CO' | 'EE';
  serieLib?: string;
  recordTime?: boolean;
}
export default function UseTimerDemo({
  expiryTimestamp,
  onExpire,
  autoStart = true,
  starting,
  mode,
  serieLib,
  recordTime,
}: Props) {
  const { data: session } = useSession();
  const time = new Date();
  const name = `${mode}_TIME_${serieLib}_${session?.user._id}`;
  let timeLess =
    typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem(name) ?? '{}').time
      : 0;
  let CurrentDate: Date;
  if (timeLess) {
    if (timeLess < 15) timeLess += 30;
    time.setSeconds(time.getSeconds() + timeLess);
    CurrentDate = time;
  } else {
    CurrentDate = expiryTimestamp;
  }
  const { seconds, minutes, hours, days, start, pause, totalSeconds } =
    useTimer({
      expiryTimestamp: CurrentDate,
      onExpire: onExpire,
      autoStart,
    });
  useEffect(() => {
    if (starting) start();
    if (!starting) pause();
  }, [starting]);
  useEffect(() => {
    if (recordTime) {
      localStorage.setItem(name, JSON.stringify({ time: totalSeconds }));
    }
  }, [totalSeconds]);
  return (
    <div>
      <TimerStyled seconds={seconds} minutes={minutes} hours={hours} />
    </div>
  );
}
