import { useEffect, useState } from 'react';
import Notification from './Notification';
import { Button, buttonVariants } from './ui/button';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import {} from '@/lib/axios';
import { UserNav } from '@/components/user-nav';

export const LeftWeb = () => {
  const { data: session } = useSession();

  return (
    <>
      {session ? (
        <>
          <Notification />
          <UserNav user={session?.user as any} />
        </>
      ) : (
        <>
          <Link
            href={'/signup'}
            className={buttonVariants({ variant: 'default' })}
          >
            Créer un compte
          </Link>
          <Link
            href={'/signin'}
            className={buttonVariants({
              variant: 'ghost',
              className: 'border',
            })}
          >
            Se connecter
          </Link>
        </>
      )}
    </>
  );
};

export const LeftMobile = () => {
  const { data: session } = useSession();
  const [mounted, setMounted] = useState(false);
  useEffect(() => setMounted(true), []);
  const router = useRouter();
  const path = usePathname();

  if (!mounted) {
    return null;
  }
  return (
    <>
      {session ? (
        <>
          <Button
            variant={'ghost'}
            onClick={() => router.push(`/notifications`)}
            className={`text-sm tracking-wider transition-all ${path.includes('notifications') ? 'bg-accent font-semibold' : 'font-normal text-gray-900'}`}
          >
            Notifications
          </Button>
        </>
      ) : (
        <>
          <Link
            href={'/signup'}
            className={buttonVariants({ variant: 'default' })}
          >
            S’INSCRIRE
          </Link>
          <Link
            href={'/signin'}
            className={buttonVariants({
              variant: 'ghost',
              className: 'border',
            })}
          >
            SE CONNECTER
          </Link>
        </>
      )}
      <Link
        href={'/checkout'}
        className={buttonVariants({ variant: 'default' })}
      >
        Payer un abonnement
      </Link>

      <Link
        href={'/renew-expression-credits/client'}
        className={buttonVariants({ variant: 'default' })}
      >
        Recharger Expression
      </Link>
    </>
  );
};
