'use client';

import { signOut, useSession } from 'next-auth/react';
import { Button } from './ui/button';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { usePath } from '@/hooks/use-path';

function RapidLinks() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const pathL = usePath();
  const router = useRouter();
  return (
    <>
      {session ? (
        <div className="mb-4 mt-2 flex max-w-[100vw] flex-wrap items-center justify-center gap-3 px-3 sm:hidden">
          <Button
            size={'sm'}
            className="text-xs"
            onClick={() => router.push('/checkout')}
          >
            Payez un abonnement
          </Button>
          <Button
            variant={'outline'}
            size={'sm'}
            className={cn('text-xs', {
              hidden: pathname.includes('/dashboard'),
            })}
            onClick={() => router.push('/dashboard/tcfhistory')}
          >
            Mon compte
          </Button>
          <Button
            variant={'destructive'}
            size={'sm'}
            className="text-xs"
            onClick={async () => {
              await signOut({
                redirect: true,
                callbackUrl: `/signin?callbackUrl=${pathL}`,
              });
            }}
          >
            Se deconnecter
          </Button>
        </div>
      ) : null}
    </>
  );
}

export default RapidLinks;
