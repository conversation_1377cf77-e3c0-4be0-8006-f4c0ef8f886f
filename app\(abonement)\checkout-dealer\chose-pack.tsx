import { motion } from 'framer-motion';
import { PathDsg } from '../checkout/ChoosePack';
import Image from 'next/image';
import mtn from '@/public/momo.jpg';
import orange from '@/public/orange.jpg';
import paypal from '@/public/paypal.svg';
import card from '@/public/master-card.svg';
import { cn } from '@/lib/utils';
import { NumericFormat } from 'react-number-format';
import { ImCheckboxChecked } from 'react-icons/im';
import { parseAsNumberLiteral, useQueryState } from 'nuqs';
import { useCountry } from '@/hooks/use-country';
import { useDealerOffers } from '@/hooks/offers';

export const getCurrency = (country: string) => {
  if (country === 'cameroun') {
    return 'F';
  } else if (country === 'afrique_ouest') {
    return 'XOF';
  } else {
    return 'EUR';
  }
};

function ChoosePack() {
  const { dealerContry } = useCountry();
  const { data: offers } = useDealerOffers();
  const [, setStep] = useQueryState(
    'step',
    parseAsNumberLiteral([1, 2]).withDefault(1),
  );
  const [, setPrice] = useQueryState('selectedPrice');
  return (
    <motion.section
      initial={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -100 }}
      transition={{
        duration: 0.5,
        type: 'tween',
      }}
      className="relative z-20 w-screen overflow-hidden bg-white pb-12 pt-10 lg:pb-[90px] lg:pt-[60px]"
    >
      <div className="container">
        <div className="-mx-4 flex flex-wrap">
          <div className="relative w-full px-4">
            <div className="mx-auto mb-24 max-w-[510px] text-center lg:mb-20">
              <span className="mb-2 block text-lg font-semibold text-primary">
                En quelques étapes
              </span>
              <h1 className="mx-auto mb-5 text-balance text-center text-4xl font-extrabold text-gray-900">
                Devenez partenaire !
              </h1>
              <h2 className="mx-auto mb-5 text-center font-sans text-2xl text-gray-900">
                Paiement{' '}
                <span className="mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
                  rapide
                </span>{' '}
                et{' '}
                <span className="mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
                  sécurisé.
                </span>{' '}
              </h2>
            </div>
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                type: 'spring',
                duration: 0.5,
                stiffness: 15,
                damping: 10,
                mass: 1,
              }}
              className="absolute bottom-8 flex items-center gap-1 self-center lg:right-20"
            >
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={mtn} alt="mtn" />
              </div>
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={orange} alt="mtn" />
              </div>
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={card} alt="mtn" />
              </div>
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={paypal} alt="mtn" />
              </div>
            </motion.div>
          </div>
        </div>
        <div className="grid gap-y-5 lg:-mx-4 lg:grid-cols-3">
          {offers
            ?.sort((a, b) => {
              if (a.cameroun.local !== b.cameroun.local) {
                return a.cameroun.local - b.cameroun.local;
              }

              return a.profilsNumber - b.profilsNumber;
            })
            .map((pricing, i) => {
              return (
                <div className={cn('grid w-full px-4')} key={i}>
                  <div className="shadow-pricing relative z-10 row-[span_9] grid grid-rows-[subgrid] items-center gap-y-5 overflow-hidden rounded-xl border border-primary border-opacity-20 bg-white px-8 py-10 sm:p-12 lg:px-6 lg:py-5 xl:p-8">
                    <h2 className="w-fit bg-orange-400/80 p-1 font-bold text-slate-800">
                      {pricing.title}
                    </h2>
                    <h3 className="text-2xl font-medium text-slate-800">
                      Validité :{' '}
                      <span className="font-semibold text-green-500">
                        {pricing.validityDays}
                      </span>{' '}
                      jours
                    </h3>
                    <div className="text-2xl">
                      <div className="flex items-end gap-4">
                        <h2 className="text-dark text-3xl font-bold">
                          <NumericFormat
                            displayType="text"
                            allowLeadingZeros
                            renderText={(value) => <>{value}</>}
                            value={pricing[dealerContry].local}
                            thousandSeparator=" "
                          />{' '}
                          {getCurrency(dealerContry)}
                          {dealerContry !== 'international' ? (
                            <span className="text-body-color text-base font-medium">
                              /{' '}
                              <NumericFormat
                                displayType="text"
                                allowLeadingZeros
                                renderText={(value) => <>{value}</>}
                                value={pricing['international'].euro}
                                thousandSeparator=" "
                              />{' '}
                              €
                            </span>
                          ) : null}
                        </h2>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <p className="text-lg font-medium italic text-slate-800">
                        {pricing.subtitle}
                      </p>
                      <p className="text-base font-bold text-slate-900">
                        {pricing.profilsNumber} profils indépendants
                      </p>
                    </div>

                    <button
                      onClick={() => {
                        setStep(2);
                        setPrice(pricing._id);
                      }}
                      className="block w-full animate-buttonheartbeat rounded-md border border-[#D4DEFF] bg-primary p-4 text-center text-sm font-semibold text-white"
                    >
                      {'Payez Maintenant'}
                    </button>
                    <hr className="" />
                    <div className="prose prose-base mb-1 prose-li:font-normal prose-li:leading-[20px] prose-li:text-slate-900">
                      <h3 className="text-lg font-bold">
                        Disciplines disponibles
                      </h3>
                      <ul className="list-item list-none space-y-4 p-0">
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit">compréhensions écrites</p>
                          </span>
                        </li>
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit">compréhensions orales</p>
                          </span>
                        </li>
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit"> Expressions écrites</p>
                          </span>
                        </li>
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit"> Expressions orales</p>
                          </span>
                        </li>
                      </ul>
                    </div>
                    <div className="prose prose-base mb-7 prose-li:m-0 prose-li:mb-2 prose-li:font-normal prose-li:leading-[20px] prose-li:text-slate-900">
                      <h3 className="text-lg font-bold">
                        Corrections professionelles
                      </h3>
                      <ul className="list-item list-none space-y-4 p-0">
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit">
                              Corrections CE illimitées
                            </p>
                          </span>
                        </li>
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit">
                              Corrections CO illimitées
                            </p>
                          </span>
                        </li>
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit">
                              Recevez{' '}
                              <span className="font-semibold">
                                {pricing.soldeEE}
                              </span>{' '}
                              corrections EE
                            </p>
                          </span>
                        </li>
                        <li>
                          <span className="m-0 flex items-center gap-1">
                            <ImCheckboxChecked className="text-emerald-400" />
                            <p className="m-0 w-fit">
                              Recevez{' '}
                              <span className="font-semibold">
                                {pricing.soldeEO}
                              </span>{' '}
                              corrections EE
                            </p>
                          </span>
                        </li>
                      </ul>
                    </div>
                    <PathDsg />
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </motion.section>
  );
}

export default ChoosePack;
