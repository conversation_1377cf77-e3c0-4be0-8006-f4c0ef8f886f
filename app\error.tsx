'use client'; // Error components must be Client Components

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { startTransition } from 'react';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();
  function handleReset() {
    startTransition(() => {
      reset();
      router.refresh();
    });
  }

  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <h2 className="mb-4 text-2xl font-bold">
        Une Erreur s&apos;est produite
      </h2>
      <div className="flex gap-2">
        <Button onClick={handleReset}>Essayer encore</Button>
        <Button onClick={() => router.push('/')} variant={'outline'}>
          Accueil
        </Button>
      </div>
    </div>
  );
}
