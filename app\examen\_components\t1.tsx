/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import { EOTask } from '@/types';
import UseTimerDemo from '@/components/timer/Time';
import { v4 as uuid } from 'uuid';
import { RefObject, useEffect, useRef, useState } from 'react';
import { Mic, Play, RefreshCcw, Square } from 'lucide-react';
import { BUCKET_BASE_URL } from '@/config';
import { Button } from '@/components/ui/button';
import { getEOtaskTime } from '@/lib/utils';
import parse from 'html-react-parser';
import { useEOStore } from '@/context/ee-provider';
import { toast } from 'sonner';
import { LiveAudioVisualizer } from 'react-audio-visualize';
const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

interface Props {
  task: EOTask;
  stopBtn: RefObject<HTMLButtonElement>;
}

export default function Taskeo1({ task, stopBtn }: Props) {
  const [isSoundLoading, setIsSoundLoading] = useState<boolean>(true);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [starting, setStarting] = useState<boolean>(false);
  const [recording, setRecording] = useState(false);
  const [isLoadErrod, setIsLoadErrod] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const setFile = useEOStore((state) => state.setFileOne);
  const setFileTwo = useEOStore((state) => state.setFileTwo);
  const setFileThree = useEOStore((state) => state.setFileThree);
  const prevFileOne = useEOStore((state) => state.prevFileOne);
  const prevFileTwo = useEOStore((state) => state.prevFileTwo);
  const prevFileThree = useEOStore((state) => state.prevFileThree);
  const prevSetFileOne = useEOStore((state) => state.prevSetFileOne);
  const prevSetFileTwo = useEOStore((state) => state.prevSetFileTwo);
  const prevSetFileThree = useEOStore((state) => state.prevSetFileThree);
  const next = useEOStore((state) => state.next);
  const [triggerEnd, setTriggerEnd] = useState(false);
  const audioref = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    navigator.mediaDevices.getUserMedia({ audio: true }).then((stream) => {
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, {
          type: 'audio/mp3',
        });
        const file = new File([blob], `${uuid()}.mp3`, { type: 'audio/mp3' });
        setFile(file);
        stream.getTracks().forEach((track) => track.stop());
        next();
      };
    });
  }, []);

  const startRecording = () => {
    recordedChunksRef.current = [];
    mediaRecorderRef.current?.start();
    setRecording(true);
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    setRecording(false);
  };

  const togglePause = () => {
    if (mediaRecorderRef.current) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        setRecording(true);
      } else {
        mediaRecorderRef.current.pause();
        setRecording(false);
      }
      setIsPaused(!isPaused);
    }
  };

  const date = new Date();
  const time = getEOtaskTime(task.numero, 'TCF');
  date.setSeconds(date.getSeconds() + time);

  useEffect(() => {
    if (triggerEnd) {
      stopBtn.current?.click();
    }
  }, [triggerEnd]);

  useEffect(() => {
    if (audioref) {
      audioref.current?.addEventListener('ended', () => {
        document?.getElementById('scroll')?.scrollIntoView();
        startRecording();
        setStarting(true);
      });
    }
    return;
  }, [audioref]);

  return (
    <div className="flex flex-col items-center justify-center gap-1 pt-1 md:px-8">
      <div className="flex w-full flex-col gap-3 text-center font-semibold md:flex-row md:justify-between">
        <div className="mx-auto flex flex-col items-center justify-center space-y-4">
          <UseTimerDemo
            expiryTimestamp={date}
            onExpire={async () => {
              stopRecording();
            }}
            autoStart={false}
            starting={starting}
          />
          <h1 className="text-base capitalize underline underline-offset-4">
            {task.libelle}
          </h1>
          <div className="prose prose-base mt-5 max-w-[50ch] text-justify font-normal">
            {parse(task.consigne)}
          </div>
          <audio
            ref={audioref}
            src={`${BUCKET_BASE_URL}/${task.fichier}`}
            onCanPlay={() => {
              setIsSoundLoading(false);
              setIsLoadErrod(false);
            }}
            onLoadedMetadata={() => {
              setIsSoundLoading(false);
              setIsLoadErrod(false);
            }}
            onError={() => {
              if (!isLoadErrod) {
                setIsLoadErrod(true);
                toast.error(
                  "Impossible de charger l'audio, veuillez réessayer.",
                  { duration: 5000, position: 'top-center' },
                );
              } else {
                setIsLoadErrod(false);
                toast.warning(
                  "Impossible de charger l'audio, lisez la description votre enregistrement va debuter dans 10s.",
                  { duration: 5000, position: 'top-center' },
                );
                setTimeout(() => {
                  startRecording();
                  setStarting(true);
                }, 10000);
              }
            }}
            className="Z-0 hidden"
          ></audio>
          <div className="flex items-center gap-3">
            <Button
              onClick={() => {
                if (!isPlaying) {
                  audioref.current?.play();
                  setIsPlaying(true);
                }
              }}
              disabled={isSoundLoading || isPlaying}
            >
              Ecoutez la consigne <Play className="ml-2 h-5 w-5 text-white" />
            </Button>
            {isLoadErrod && (
              <Button
                size={'icon'}
                variant={'outline'}
                onClick={() => audioref.current?.load()}
              >
                <RefreshCcw className="h-5 w-5 text-blue-500" />
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="mt-5 flex w-full flex-col items-center justify-center gap-1">
        <div className="flex w-full flex-col items-center justify-center gap-1">
          <h1 className="text-lg font-semibold">Enregistrement</h1>
          {mediaRecorderRef.current != undefined ? (
            <LiveAudioVisualizer
              mediaRecorder={mediaRecorderRef.current}
              width={200}
              height={75}
            />
          ) : null}
          <div className="mx-auto flex w-fit items-center justify-between gap-1">
            <Button
              disabled={!starting}
              size={'icon'}
              onClick={() => {
                if (recording && !isPaused) {
                  stopRecording();
                } else {
                  startRecording();
                }
              }}
              className={`group h-[50px] w-[50px] rounded-full bg-red-500 hover:bg-zinc-100`}
            >
              {recording && !isPaused ? (
                <Square className="h-6 w-6 text-white group-hover:text-red-500" />
              ) : (
                <Mic className="h-6 w-6 text-white group-hover:text-red-500" />
              )}
            </Button>
            <Button disabled={!starting} onClick={togglePause}>
              {isPaused ? 'Reprendre' : 'Pause'}
            </Button>
          </div>
        </div>
      </div>

      {prevFileOne && prevFileTwo && prevFileThree ? (
        <div className="my-5 w-full max-w-md space-y-4 rounded-lg border p-4 shadow-sm">
          <h1 className="text-lg font-semibold">Vos enregistrements</h1>
          <div className="flex flex-col gap-4">
            <h2>Tâche 1</h2>
            <audio
              className="w-full"
              src={
                isSafari
                  ? webkitURL.createObjectURL(
                      new Blob([prevFileOne], { type: prevFileOne.type }),
                    )
                  : URL.createObjectURL(
                      new Blob([prevFileOne], { type: prevFileOne.type }),
                    )
              }
              controls
            />
            <h2>Tâche 2</h2>
            <audio
              className="w-full"
              src={
                isSafari
                  ? webkitURL.createObjectURL(
                      new Blob([prevFileTwo], { type: prevFileTwo.type }),
                    )
                  : URL.createObjectURL(
                      new Blob([prevFileTwo], { type: prevFileTwo.type }),
                    )
              }
              controls
            />
            <h2>Tâche 3</h2>
            <audio
              className="w-full"
              src={
                isSafari
                  ? webkitURL.createObjectURL(
                      new Blob([prevFileThree], { type: prevFileThree.type }),
                    )
                  : URL.createObjectURL(
                      new Blob([prevFileThree], { type: prevFileThree.type }),
                    )
              }
              controls
            />
          </div>
          <div className="flex items-center justify-center gap-4">
            <Button
              variant={'destructive'}
              onClick={() => {
                prevSetFileOne(null);
                prevSetFileTwo(null);
                prevSetFileThree(null);
              }}
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                prevSetFileOne(null);
                prevSetFileTwo(null);
                prevSetFileThree(null);
                setFile(prevFileOne);
                setFileTwo(prevFileTwo);
                setFileThree(prevFileThree);
                toast.success('Enregistrements restaurés avec succès.');
                setTriggerEnd(true);
              }}
            >
              Accepter
            </Button>
          </div>
        </div>
      ) : null}
      <div id="scroll"></div>
    </div>
  );
}
