'use client';

import React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { isProfile } from '@/lib/utils';

interface Tab {
  name: string;
  href: string;
  active?: boolean;
}

export default function DashboardNavigation() {
  const { data: session } = useSession();
  const pathname = usePathname();

  const tabs: Tab[] = [
    { name: 'Corrections TCF', href: '/dashboard/tcfhistory', active: true },
    { name: 'Corrections TEF', href: '/dashboard/tefhistory', active: true },
    { name: 'Mon compte', href: '/dashboard/account', active: true },
    {
      name: 'Profiles',
      href: '/dashboard/profiles',
      active: !isProfile(session?.user.email),
    },
  ];

  return (
    <div className="mx-4 my-3">
      <Tabs
        defaultValue={tabs.find((tab) => pathname.startsWith(tab.href))?.href}
        className="mr-auto hidden w-fit sm:flex"
      >
        <TabsList className="flex space-x-4">
          {tabs
            .filter((tab) => tab.active)
            .map((tab) => (
              <Link key={tab.href} href={tab.href}>
                <TabsTrigger
                  value={tab.href}
                  className={`${
                    pathname.startsWith(tab.href)
                      ? '!bg-blue-500 !text-white'
                      : '!text-gray-700'
                  }`}
                >
                  {tab.name}
                </TabsTrigger>
              </Link>
            ))}
        </TabsList>
      </Tabs>

      <div className="flex w-full flex-col gap-2 sm:hidden">
        <Tabs
          defaultValue={tabs.find((tab) => pathname.startsWith(tab.href))?.href}
          className="mr-auto w-fit"
        >
          <TabsList className="flex space-x-4">
            {tabs
              .filter((tab) => tab.active)
              .slice(0, 2)
              .map((tab) => (
                <Link key={tab.href} href={tab.href}>
                  <TabsTrigger
                    value={tab.href}
                    className={`${
                      pathname.startsWith(tab.href)
                        ? '!bg-blue-500 !text-white'
                        : '!text-gray-700'
                    }`}
                  >
                    {tab.name}
                  </TabsTrigger>
                </Link>
              ))}
          </TabsList>
        </Tabs>
        <Tabs
          defaultValue={tabs.find((tab) => pathname.startsWith(tab.href))?.href}
          className="mr-auto w-fit"
        >
          <TabsList className="flex space-x-4">
            {tabs
              .filter((tab) => tab.active)
              .slice(2, 4)
              .map((tab) => (
                <Link key={tab.href} href={tab.href}>
                  <TabsTrigger
                    value={tab.href}
                    className={`${
                      pathname.startsWith(tab.href)
                        ? '!bg-blue-500 !text-white'
                        : '!text-gray-700'
                    }`}
                  >
                    {tab.name}
                  </TabsTrigger>
                </Link>
              ))}
          </TabsList>
        </Tabs>
      </div>
    </div>
  );
}
