import { getScore, getTestSet } from '@/lib/utils';
import {
  ResultatResponse,
  Resultset,
  Row,
  ScoreC,
  Serie,
  TestSet,
} from '@/types';
import { User } from 'next-auth';
import { create } from 'zustand';

export const MODE = {
  CE: 'CE',
  CO: 'CO',
  EE: 'EE',
  EO: 'EO',
  ALL: 'ALL',
} as const;

export type ObjectValues<T> = T[keyof T];

export type Mode = ObjectValues<typeof MODE>;

interface TestState {
  mode: Mode;
  isTart: boolean;
  isEnd: boolean;
  Start: () => void;
  Stop: () => void;
  SetMode: (value: Mode) => void;
  isFree: Boolean;
  setIsFree: (value: Boolean) => void;
  current: number;
  detail: ResultatResponse | undefined;
  setDetail: (val: ResultatResponse | undefined) => void;
  next: () => void;
  prev: () => void;
  setCurrent: (index: number) => void;
  resulSet: Resultset[];
  Addset: (value: Resultset) => void;
  setresSet: (value: Resultset[]) => void;
  Restart: () => void;
  testSet: TestSet | null;
  score: ScoreC | null;
  serie: Serie | null;
  selectedRow: Row | null;
  setSelectedRow: (value: Row) => void;
  setTestSet: (serie: Serie) => void;
  endtext: string;
  setEndtext: (val: string) => void;
  user: User | null;
  setUser: (user: User) => void;
}
export const useTestState = create<TestState>((set) => ({
  // Définissez votre état initial ici
  endtext: '',
  isTart: false,
  detail: undefined,
  isFree: true,
  isEnd: false,
  mode: 'CE',
  selectedRow: null,
  current: 0,
  testSet: null,
  serie: null,
  score: null,
  user: null,
  setUser: (user) => set(() => ({ user: user })),
  setEndtext: (val) => set(() => ({ endtext: val })),
  setSelectedRow: (value) => set((state) => ({ selectedRow: value })),
  Restart: () =>
    set((state) => {
      return {
        isEnd: false,
        isTart: false,
        resulSet: [],
        current: 0,
        testSet: null,
        detailId: '',
        score: null,
        serie: null,
        detail: undefined,
        selectedRow: null,
      };
    }),
  setIsFree: (value) => set((state) => ({ isFree: value })),
  setDetail: (value) => set((state) => ({ detail: value })),
  next: () => set((state) => ({ current: state.current + 1 })),
  prev: () => set((state) => ({ current: state.current - 1 })),
  setCurrent: (index) => set((state) => ({ current: index })),
  Start: () => set((state) => ({ isTart: true, isEnd: false })),
  Stop: () =>
    set((state) => {
      const res = getScore(state.testSet!, state.resulSet);
      return { isEnd: true, isTart: false, score: res };
    }),
  SetMode: (value) => set((state) => ({ mode: value })),
  resulSet: [],
  Addset: (value) =>
    set((state) => {
      const RES_NAME = `${state.mode}_RES_${state.testSet?.libelle}_${state.user?._id}`;
      const tab = state.resulSet.filter(
        (res) => res.questionId !== value.questionId,
      );
      tab.push(value);
      localStorage.setItem(RES_NAME, JSON.stringify(tab));
      return { resulSet: tab };
    }),
  setTestSet: (serie) =>
    set((state) => {
      const res = getTestSet(serie);
      return { testSet: res, serie: serie };
    }),
  setresSet: (value) => {
    set(() => {
      return { resulSet: value };
    });
  },
}));
