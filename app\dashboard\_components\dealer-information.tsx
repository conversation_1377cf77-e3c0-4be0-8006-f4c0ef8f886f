'use client';
import { User } from 'next-auth';
import RemainDay from './Remainday';
import { getDayCount } from '@/lib/getDayCount';
import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';
import { useGetProfilesQuery } from '@/hooks/useGetProfilesQuery';
import { Loader2 } from 'lucide-react';

export const DealerInformation = ({ user }: { user: User }) => {
  const abonnementRemainDay = getDayCount(
    user?.remainsDeals?.remainTCF?.remain_day || null,
  );

  const { data, isLoading } = useGetProfilesQuery({
    isEnable: user.role == 'dealer',
  });
  return (
    <div
      className={`grid grid-cols-1 items-center gap-6 rounded-lg md:grid-cols-3`}
    >
      <div className="">
        <h2 className="mb-4 text-xl font-semibold text-gray-800">
          Information
        </h2>
        <div className="prose max-w-none space-y-1">
          <p className="m-0">
            <span className="font-semibold">Email:</span> {user.email}{' '}
          </p>
          <p>
            <span className="font-semibold">Solde expression écrite:</span>{' '}
            {user.remainsDeals?.remainTCF?.balance_ee
              .toString()
              .padStart(2, '0')}{' '}
          </p>
          <p>
            <span className="font-semibold">Solde expression orale:</span>{' '}
            {user.remainsDeals?.remainTCF?.balance_eo
              .toString()
              .padStart(2, '0')}{' '}
          </p>
          {user.remainsDeals?.remainTCF?.profilsLimits && (
            <p className="inline-flex items-center">
              <span className="mr-2 font-semibold">Nombre de profil:</span>{' '}
              <>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  data?.length.toString().padStart(2, '0')
                )}{' '}
                /{' '}
              </>
              {user.remainsDeals?.remainTCF?.profilsLimits}
            </p>
          )}
        </div>
      </div>
      <RemainDay abonnementRemainDay={abonnementRemainDay} />
      <div className="max-w-sm">
        <h2 className="mb-4 text-xl font-semibold text-gray-800">
          Actions rapides
        </h2>
        <div className="flex flex-col gap-3">
          <Link
            href={'/checkout-dealer'}
            className={buttonVariants({ variant: 'secondary' })}
          >
            Renouvelez votre abonnement
          </Link>
          <Link
            href={'/renew-expression-credits/dealer'}
            className={buttonVariants({ variant: 'secondary' })}
          >
            Recharge des expressions
          </Link>
          <Link
            href={'profiles/correction'}
            className={buttonVariants({ variant: 'secondary' })}
          >
            Vos corrections
          </Link>
        </div>
      </div>
    </div>
  );
};
