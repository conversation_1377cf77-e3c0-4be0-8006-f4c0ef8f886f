'use client';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { z } from 'zod';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Loader2 } from 'lucide-react';
import axios, { AxiosError } from 'axios';
import { toast } from '../ui/use-toast';
import { useRouter } from 'next/navigation';
import { Label } from '../ui/label';
import { parseAsBoolean, useQueryState } from 'nuqs';

export default function ResetForm() {
  const router = useRouter();
  const [hasOtp, setHasOtp] = useQueryState(
    'hasOtp',
    parseAsBoolean.withDefault(false),
  );
  const [email, setEmail] = useQueryState('email', { defaultValue: '' });
  const EmailSchema = z.object({
    email: z.string().email({ message: 'Email invalide' }),
  });
  const ChangeSchema = z
    .object({
      otp: z
        .string()
        .min(4, { message: 'Code invalide' })
        .max(4, { message: 'Code invalide' }),
      password: z
        .string()
        .min(8, { message: 'Mot de passe invalide minimum 8 caractères' }),
      password_confirmation: z
        .string()
        .min(8, { message: 'Mot de passe invalide minimum 8 caractères' }),
    })
    .refine((data) => data.password == data.password_confirmation, {
      message: 'Mot de passe ne correspond pas',
      path: ['password_confirmation'],
    });
  type EmailSchemaType = z.infer<typeof EmailSchema>;
  const emailForm = useForm<EmailSchemaType>({
    defaultValues: {
      email: '',
    },
    resolver: zodResolver(EmailSchema),
  });
  const onSubmitEmail: SubmitHandler<EmailSchemaType> = async (
    { email },
    e,
  ) => {
    e?.preventDefault();
    try {
      const res = await axios.post(
        `https://abjectof-conoda2.onrender.com/api/otp/forgot-password`,
        {
          email,
        },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      console.log(res.data);
      setEmail(res.data.email);
      setHasOtp(true);
      emailForm.reset();
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.code === 'ERR_BAD_REQUEST') {
          return toast({
            title: 'Aucun compte trouvé avec cet email',
            variant: 'destructive',
          });
        }
        if (
          error.code === 'ERR_NETWORK' ||
          error.code === 'ETIMEDOUT' ||
          error.code === 'ENOTFOUND'
        ) {
          return toast({
            title: 'Verifier votre connexion internet',
            variant: 'destructive',
          });
        }
      }
      return toast({
        title: 'Erreur inconnue',
        description: 'Veuillez réessayer',
        variant: 'destructive',
      });
    }
  };
  type ChangeSchemaType = z.infer<typeof ChangeSchema>;
  const changeForm = useForm<ChangeSchemaType>({
    defaultValues: {
      otp: '',
      password: '',
      password_confirmation: '',
    },
    resolver: zodResolver(ChangeSchema),
  });
  const onSubmitChange: SubmitHandler<ChangeSchemaType> = async (
    { otp, password },
    e,
  ) => {
    e?.preventDefault();
    try {
      const res = await axios.post(
        `https://abjectof-conoda2.onrender.com/api/auth/reset-password`,
        {
          email,
          otp,
          newPassword: password,
        },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      toast({
        title: 'Bravo votre mot de passe a été modifié',
      });
      router.replace('/signin');
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.code === 'ERR_BAD_REQUEST') {
          return toast({
            title: 'Code invalide ou expiré',
            variant: 'destructive',
          });
        }
        if (
          error.code === 'ERR_NETWORK' ||
          error.code === 'ETIMEDOUT' ||
          error.code === 'ENOTFOUND'
        ) {
          return toast({
            title: 'Verifier votre connexion internet',
            variant: 'destructive',
          });
        }
      }
      return toast({
        title: 'Erreur inconnue',
        description: 'Veuillez réessayer',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      {hasOtp ? (
        <Form {...changeForm}>
          <form
            onSubmit={changeForm.handleSubmit(onSubmitChange)}
            noValidate
            className="grid gap-3"
          >
            <h2 className="text-center text-2xl font-bold">
              Changer votre mot de passe
            </h2>
            <p className="text-center text-sm text-muted-foreground">
              Entrez le code que vous avez reçu par email.
            </p>
            <FormField
              control={changeForm.control}
              name="otp"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Code</FormLabel>
                  <FormControl>
                    <Input id="otp" tabIndex={1} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={changeForm.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nouveau mot de passe</FormLabel>
                  <FormControl>
                    <Input tabIndex={2} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={changeForm.control}
              name="password_confirmation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirmation du mot de passe</FormLabel>
                  <FormControl>
                    <Input tabIndex={3} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              tabIndex={4}
              disabled={changeForm.formState.isSubmitting}
              type="submit"
              className="mt-3 w-full"
            >
              {changeForm.formState.isSubmitting ? (
                <Loader2 className="h-6 w-6 animate-spin" />
              ) : (
                'Envoyer'
              )}
            </Button>
          </form>
        </Form>
      ) : (
        <form
          onSubmit={emailForm.handleSubmit(onSubmitEmail)}
          noValidate
          className="grid gap-3"
        >
          <h2 className="text-center text-2xl font-bold">
            Mot de passe oublié ? Pas de soucis
          </h2>
          <p className="text-center text-sm text-muted-foreground">
            Entrez votre email pour le changer.
          </p>

          <Label className="sr-only" htmlFor="email">
            Email
          </Label>
          <Input
            id="email"
            tabIndex={1}
            placeholder="Adresse mail"
            type="email"
            autoCapitalize="none"
            autoComplete="email"
            autoCorrect="off"
            disabled={emailForm.formState.isSubmitting}
            {...emailForm.register('email', {
              required: {
                value: true,
                message: 'Ce champ est obligatoire',
              },
            })}
          />

          <Button
            disabled={emailForm.formState.isSubmitting}
            type="submit"
            className="mt-3 w-full"
          >
            {emailForm.formState.isSubmitting ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              'Continuer'
            )}
          </Button>
        </form>
      )}
    </>
  );
}
