'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { SessionProvider } from 'next-auth/react';
import { Toaster } from './ui/toaster';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster as SonnerToaster, toast } from 'sonner';
import type { Session } from 'next-auth';
import TopLoader from '@/components/toploader-provider';
import TimeAgo from 'javascript-time-ago';
import en from 'javascript-time-ago/locale/en.json';
import fr from 'javascript-time-ago/locale/fr.json';
import CookieBanner from './Analytics/CookieBanner';
import RefreshSession from './CheckConnection';

interface Props {
  children: ReactNode;
  session?: Session;
}
function Provider({ children, session }: Props) {
  const [queryClient] = useState(new QueryClient());
  useEffect(() => {
    const handleContextMenu = (event: MouseEvent) => {
      if (
        process.env.NODE_ENV === 'production' &&
        window.location.hostname !== 'oc-testdev.com'
      ) {
        event.preventDefault();
      }
    };
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        process.env.NODE_ENV === 'production' &&
        window.location.hostname !== 'oc-testdev.com'
      ) {
        if (
          event.ctrlKey &&
          (event.key === 'u' ||
            event.key === 'c' ||
            event.key === 's' ||
            event.key === 'p')
        ) {
          event.preventDefault();
        }

        if (event.key == 'F12') {
          event.preventDefault();
        }
        if (event.ctrlKey && event.shiftKey && event.key === 'i')
          event.preventDefault();
      }
    };
    TimeAgo.addDefaultLocale(en);
    TimeAgo.addLocale(fr);

    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  useEffect(() => {
    const navHeight = document.querySelector('#nav-bar')?.clientHeight;
    document.documentElement.style.setProperty(
      '--scroll-padding',
      navHeight + 'px',
    );
  }, []);

  useEffect(() => {
    const listener = () => {
      const context = new window.AudioContext();
      context.resume();
    };
    document.addEventListener('touchend', listener);
    return () => {
      document.removeEventListener('touchend', listener);
    };
  }, []);

  useEffect(() => {
    const handleOnline = () => {
      toast.success('Vous êtes connecté');
    };

    const handleOffline = () => {
      toast.error('Vous êtes hors ligne');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {};
  }, []);

  return (
    <SessionProvider
      session={session}
      refetchInterval={60 * 60}
      refetchWhenOffline={false}
      refetchOnWindowFocus={true}
    >
      <QueryClientProvider client={queryClient}>
        {children}
        <Toaster />
        <TopLoader />
        <RefreshSession />
        <SonnerToaster visibleToasts={1} position="bottom-right" richColors />
        <CookieBanner />
      </QueryClientProvider>
    </SessionProvider>
  );
}

export default Provider;
