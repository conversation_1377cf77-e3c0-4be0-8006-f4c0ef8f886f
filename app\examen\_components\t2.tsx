/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import { EOTask } from '@/types';
import UseTimerDemo from '@/components/timer/Time';
import { v4 as uuid } from 'uuid';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { RefObject, useEffect, useRef, useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { Mic, Play, RefreshCcw, Square } from 'lucide-react';
import { BUCKET_BASE_URL } from '@/config';
import { Button } from '@/components/ui/button';
import { getEOtaskTime } from '@/lib/utils';
import parse from 'html-react-parser';
import { useEOStore } from '@/context/ee-provider';
import { LiveAudioVisualizer } from 'react-audio-visualize';
import { toast } from 'sonner';

interface Props {
  task: EOTask;
  stopBtn: RefObject<HTMLButtonElement>;
}

export default function Taskeo2({ task }: Props) {
  const [isSoundLoading, setIsSoundLoading] = useState<boolean>(true);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [starting, setStarting] = useState<boolean>(false);
  const [isOpenP, setIsOpenP] = useState<boolean>(false);
  const [recording, setRecording] = useState(false);
  const [intervalPrep, setIntervalPrep] = useState<NodeJS.Timer | null>(null);
  const [PrepProgress, setPrepProgress] = useState<number>(100);
  const [isLoadErrod, setIsLoadErrod] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const setFile = useEOStore((state) => state.setFileTwo);

  const next = useEOStore((state) => state.next);
  const audioref = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    navigator.mediaDevices.getUserMedia({ audio: true }).then((stream) => {
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, {
          type: 'audio/mp3',
        });
        const file = new File([blob], `${uuid()}.mp3`, { type: 'audio/mp3' });
        setFile(file);
        stream.getTracks().forEach((track) => track.stop());
        next();
      };
    });
  }, []);

  const startRecording = () => {
    recordedChunksRef.current = [];
    mediaRecorderRef.current?.start();
    setRecording(true);
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    setRecording(false);
  };

  const togglePause = () => {
    if (mediaRecorderRef.current) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        setRecording(true);
      } else {
        mediaRecorderRef.current.pause();
        setRecording(false);
      }
      setIsPaused(!isPaused);
    }
  };
  const startPreparation = (): NodeJS.Timer => {
    setPrepProgress(100);
    setIsOpenP(true);
    const interval = setInterval(() => {
      setPrepProgress((prevProgress) => {
        if (prevProgress <= 0) {
          stopPreparation(interval);
          return prevProgress;
        }
        return prevProgress - 0.84;
      });
    }, 1000);

    return interval;
  };

  const stopPreparation = (interval: NodeJS.Timer) => {
    clearInterval(interval);
    setIsOpenP(false);
    startRecording();
    setStarting(true);
  };

  const date = new Date();
  const time = getEOtaskTime(task.numero, 'TCF');
  date.setSeconds(date.getSeconds() + time);

  useEffect(() => {
    if (audioref) {
      audioref.current?.addEventListener('ended', () => {
        setIntervalPrep(startPreparation());
      });
    }
    return;
  }, [audioref]);

  return (
    <div className="flex flex-col items-center justify-center gap-1 pt-1 md:px-8">
      <div className="flex w-full flex-col gap-3 text-center font-semibold md:flex-row md:justify-between">
        <div className="mx-auto flex flex-col items-center justify-center space-y-4">
          <UseTimerDemo
            expiryTimestamp={date}
            onExpire={async () => {
              stopRecording();
            }}
            autoStart={false}
            starting={starting}
          />
          <h1 className="text-base capitalize underline underline-offset-4">
            {task.libelle}
          </h1>
          <div className="prose prose-base mt-5 max-w-[50ch] text-justify font-normal">
            {parse(task.consigne)}
          </div>
          <audio
            ref={audioref}
            src={`${BUCKET_BASE_URL}/${task.fichier}`}
            onCanPlay={() => {
              setIsSoundLoading(false);
              setIsLoadErrod(false);
            }}
            onLoadedMetadata={() => {
              setIsSoundLoading(false);
              setIsLoadErrod(false);
            }}
            onError={() => {
              if (!isLoadErrod) {
                setIsLoadErrod(true);
                toast.error(
                  "Impossible de charger l'audio, veuillez réessayer.",
                  { duration: 5000, position: 'top-center' },
                );
              } else {
                setIsLoadErrod(false);
                toast.warning(
                  "Impossible de charger l'audio, lisez la description votre enregistrement va debuter dans 10s.",
                  { duration: 5000, position: 'top-center' },
                );
                setTimeout(() => {
                  startRecording();
                  setStarting(true);
                }, 10000);
              }
            }}
            className="Z-0 hidden"
          ></audio>
          <div className="flex items-center gap-3">
            <Button
              onClick={() => {
                if (!isPlaying) {
                  audioref.current?.play();
                  setIsPlaying(true);
                }
              }}
              disabled={isSoundLoading || isPlaying}
            >
              Ecoutez la consigne <Play className="ml-2 h-5 w-5 text-white" />
            </Button>
            {isLoadErrod && (
              <Button
                size={'icon'}
                variant={'outline'}
                onClick={() => audioref.current?.load()}
              >
                <RefreshCcw className="h-5 w-5 text-blue-500" />
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="mt-5 flex w-full flex-col items-center justify-center gap-1">
        <div className="flex w-full flex-col items-center justify-center gap-1">
          <h1 className="text-lg font-semibold">Enregistrement</h1>
          {mediaRecorderRef.current != undefined ? (
            <LiveAudioVisualizer
              mediaRecorder={mediaRecorderRef.current}
              width={200}
              height={75}
            />
          ) : null}
          <div className="mx-auto flex w-fit items-center justify-between gap-1">
            <Button
              disabled={!starting}
              size={'icon'}
              onClick={() => {
                if (recording && !isPaused) {
                  stopRecording();
                } else {
                  startRecording();
                }
              }}
              className={`group h-[50px] w-[50px] rounded-full bg-red-500 hover:bg-zinc-100`}
            >
              {recording && !isPaused ? (
                <Square className="h-6 w-6 text-white group-hover:text-red-500" />
              ) : (
                <Mic className="h-6 w-6 text-white group-hover:text-red-500" />
              )}
            </Button>
            <Button disabled={!starting} onClick={togglePause}>
              {isPaused ? 'Reprendre' : 'Pause'}
            </Button>
          </div>
        </div>
      </div>
      <div id="scroll"></div>
      <AlertDialog open={isOpenP}>
        <AlertDialogTrigger asChild>
          <button type="button" title="stop" className="hidden">
            blabla
          </button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="prose prose-lg text-center uppercase">
              Temps de préparation
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <section>
                <div className="p-8">
                  {isOpenP ? (
                    <div className="mx-auto mt-4 w-full max-w-xs">
                      <Progress
                        indicatorColor={PrepProgress <= 50 ? 'bg-red-500' : ''}
                        value={PrepProgress}
                        className="h-1 w-full bg-zinc-200 transition-all"
                      />
                    </div>
                  ) : null}
                </div>
                <div className="w-full">
                  <h1 className="text-center text-xl font-bold">Consigne</h1>
                  <div className="prose prose-base mt-5 max-w-[50ch] text-justify font-normal">
                    {parse(task.consigne)}
                  </div>
                </div>
              </section>
            </AlertDialogDescription>
            <Button
              className="ml-auto mr-3"
              variant={'outline'}
              onClick={() => stopPreparation(intervalPrep!)}
            >
              Débuter
            </Button>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
