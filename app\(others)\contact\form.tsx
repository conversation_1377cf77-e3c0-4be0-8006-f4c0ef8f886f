'use client';
import { useTranslation } from '@/app/i18n/client';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { usei18nState } from '@/context/i18n';
import { useDocumentTitle } from '@mantine/hooks';
import { SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { sendEmail } from '@/actions';
import { useToast } from '@/components/ui/use-toast';
import { ContactFormValidator, TContactform } from '@/schema';
function ContactForm() {
  const { toast } = useToast();
  const { lng } = usei18nState();
  const { t } = useTranslation(lng);
  useDocumentTitle(`TCF | ${t('Contact', { ns: 'navbar' })}`);
  const {
    register,
    reset,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<TContactform>({
    resolver: zod<PERSON><PERSON>olver(ContactFormValidator),
  });
  const onSubmit: SubmitHandler<TContactform> = async (data) => {
    const result = await sendEmail(data);

    if (result?.success) {
      console.log({ data: result.data });
      toast({
        title: 'Email envoyé!',
      });
      reset();
      return;
    }

    // toast error
    console.log(result?.error);
    toast({
      title: 'Something went wrong!',
      description: 'retry later',
      variant: 'destructive',
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-3">
      <div className="grid gap-2">
        <Label htmlFor="name">Votre nom</Label>
        <Input id="name" type="text" {...register('name')} />
        {errors.name?.message && (
          <p className="ml-1 mt-1 text-sm text-red-400">
            {errors.name.message}
          </p>
        )}
      </div>
      <div className="grid gap-2">
        <Label htmlFor="email">Votre email</Label>
        <Input id="email" type="email" {...register('email')} />
        {errors.email?.message && (
          <p className="ml-1 mt-1 text-sm text-red-400">
            {errors.email.message}
          </p>
        )}
      </div>
      <div className="grid gap-2">
        <Label htmlFor="phone">Votre numéro de téléphone</Label>
        <Input id="phone" type="tel" {...register('phone')} />
        {errors.phone?.message && (
          <p className="ml-1 mt-1 text-sm text-red-400">
            {errors.phone.message}
          </p>
        )}
      </div>
      <div className="grid gap-2">
        <Label htmlFor="msg">Votre message</Label>
        <Textarea
          autoCapitalize="off"
          id="msg"
          rows={6}
          {...register('message')}
        />
        {errors.message?.message && (
          <p className="ml-1 mt-1 text-sm text-red-400">
            {errors.message.message}
          </p>
        )}
      </div>
      <div>
        <button
          disabled={isSubmitting}
          type="submit"
          className="w-full rounded bg-blue-500 p-3 text-white transition hover:bg-opacity-90 disabled:cursor-not-allowed disabled:bg-blue-700/50"
        >
          {isSubmitting ? 'Submitting...' : 'ENVOYER'}
        </button>
      </div>
    </form>
  );
}

export default ContactForm;
