#!/bin/bash

# Script d'installation pour TanStack Virtual
echo "🚀 Installation de TanStack Virtual pour la virtualisation des pays..."

# Installation de TanStack Virtual
npm install @tanstack/react-virtual

echo "✅ TanStack Virtual installé avec succès !"

# Vérification de l'installation
echo "📦 Vérification de l'installation..."
npm list @tanstack/react-virtual

echo "🎉 Installation terminée ! Vous pouvez maintenant utiliser la virtualisation des pays."
echo ""
echo "📝 Pour tester l'implémentation :"
echo "   1. D<PERSON><PERSON>rez le serveur de développement : npm run dev"
echo "   2. Visitez : http://localhost:3000/test-countries"
echo ""
echo "📚 Documentation disponible dans : docs/COUNTRIES_LAZY_LOADING.md"
