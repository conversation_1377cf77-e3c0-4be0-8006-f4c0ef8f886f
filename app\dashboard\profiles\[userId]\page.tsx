'use client';
import { useParams } from 'next/navigation';
import {
  getProfileComprehensionResult,
  getProfileExpressionResult,
} from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import HistoryLoader from '../../_components/loader-history';
import { BackToPrfile } from '../../_components/back-button';
import ProfileTCFContentDashboard from '../../_components/profile-tcf-history';
import { useGetProfilesQuery } from '@/hooks/useGetProfilesQuery';

function ProfileDashboard() {
  const { userId } = useParams();
  const { data: profiles, isLoading: isProfileLoading } = useGetProfilesQuery({
    isEnable: true,
  });
  const { data, isLoading, isError } = useQuery(
    ['profile-tcf-data', { profileId: userId }],
    async () => {
      const [CERes, CORes, EERes, EORes] = await Promise.all([
        getProfileComprehensionResult('CE', userId as string),
        getProfileComprehensionResult('CO', userId as string),
        getProfileExpressionResult('EE', userId as string),
        getProfileExpressionResult('EO', userId as string),
      ]);
      return { CERes, CORes, EERes, EORes };
    },
  );

  if (isError) {
    return (
      <div className="grid place-content-center">
        <h1>Failed to load data. Please try again later.</h1>
      </div>
    );
  }

  if (isLoading || isProfileLoading || !data) {
    return <HistoryLoader />;
  }

  const { CERes, CORes, EERes, EORes } = data!;
  const dataC = [
    ...(CERes?.data || []).map((d) => ({ ...d, type: 'CE' })),
    ...(CORes?.data || []).map((d) => ({ ...d, type: 'CO' })),
  ];

  const error = CERes?.error || CORes?.error || EERes?.error || EORes?.error;
  const profile = profiles?.find((p) => p.userId === userId);
  return (
    <div className="space-y-3">
      <div className="relative text-center">
        <div className="sm:absolute">
          <BackToPrfile />
        </div>
        <span className="text-xl underline underline-offset-4">
          Historique des résultats TCF de{' '}
          <span className="text-2xl font-bold">
            {profile?.nom_profil || 'Profil Inconnu'}
          </span>
        </span>
      </div>
      <ProfileTCFContentDashboard
        dataC={dataC.filter((d) => d.serie)}
        dataE={EERes?.data.filter((d) => d.serie)}
        dataO={EORes?.data.filter((d) => d.serie)}
        error={error}
        remains={profile?.remains?.remainTCF}
      />
    </div>
  );
}

export default ProfileDashboard;
