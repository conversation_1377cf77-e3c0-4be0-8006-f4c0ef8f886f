import { Suspense } from 'react';
import {
  ExpressionLoader,
  OralExpression,
  WritingExpression,
} from './_components/server-table';
import { BackToPrfile } from '../../_components/back-button';

function ProfileDashboard() {
  return (
    <main className="container relative mx-auto flex max-w-[100dvw] flex-col gap-6 overflow-hidden p-4">
      <BackToPrfile />
      <div className="flex flex-col gap-3">
        <h1 className="text-xl font-bold text-gray-800 lg:text-2xl">
          Sujets d'expression écrite
        </h1>
        <Suspense fallback={<ExpressionLoader />}>
          <WritingExpression />
        </Suspense>
      </div>

      <div className="flex flex-col gap-3">
        <h1 className="text-xl font-bold text-gray-800 lg:text-2xl">
          Sujets d'expression orale
        </h1>
        <Suspense fallback={<ExpressionLoader />}>
          <OralExpression />
        </Suspense>
      </div>
    </main>
  );
}

export default ProfileDashboard;
