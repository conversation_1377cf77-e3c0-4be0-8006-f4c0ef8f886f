# Script d'installation PowerShell pour TanStack Virtual
Write-Host "🚀 Installation de TanStack Virtual pour la virtualisation des pays..." -ForegroundColor Green

# Installation de TanStack Virtual
Write-Host "📦 Installation en cours..." -ForegroundColor Yellow
npm install @tanstack/react-virtual

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ TanStack Virtual installé avec succès !" -ForegroundColor Green
    
    # Vérification de l'installation
    Write-Host "📦 Vérification de l'installation..." -ForegroundColor Yellow
    npm list @tanstack/react-virtual
    
    Write-Host "🎉 Installation terminée ! Vous pouvez maintenant utiliser la virtualisation des pays." -ForegroundColor Green
    Write-Host ""
    Write-Host "📝 Pour tester l'implémentation :" -ForegroundColor Cyan
    Write-Host "   1. Démarrez le serveur de développement : npm run dev" -ForegroundColor White
    Write-Host "   2. Visitez : http://localhost:3000/test-countries" -ForegroundColor White
    Write-Host ""
    Write-Host "📚 Documentation disponible dans : docs/COUNTRIES_LAZY_LOADING.md" -ForegroundColor Cyan
} else {
    Write-Host "❌ Erreur lors de l'installation. Vérifiez votre connexion internet et réessayez." -ForegroundColor Red
}
