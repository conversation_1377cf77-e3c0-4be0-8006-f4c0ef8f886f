import UserInfo from '../UserInfo';
import { getAuthSession } from '@/lib/auth';
import { redirect } from 'next/navigation';
import DashboardNavigation from '../_components/navigation';

export default async function Page() {
  const session = await getAuthSession();
  if (!session) redirect('/signin');
  return (
    <>
      <DashboardNavigation />
      <div className="mx-auto w-full md:container">
        <UserInfo severSession={session} />
      </div>
    </>
  );
}
