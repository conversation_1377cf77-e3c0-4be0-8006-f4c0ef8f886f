import { getAuthSession } from '@/lib/auth';
import { AuthError } from '@/types/error';
import { redirect } from 'next/navigation';

export class AuthService {
  constructor() {}
  async getUser() {
    try {
      const session = await getAuthSession();
      if (!session) {
        console.log('redirect from auth service');

        redirect('/auth');
      }
      return session.user;
    } catch (error) {
      console.log(error);

      throw new AuthError();
    }
  }
}
