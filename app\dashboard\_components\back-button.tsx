import { buttonVariants } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export const BackToPrfile = () => {
  return (
    <Link
      href="/dashboard/profiles"
      className={buttonVariants({
        variant: 'outline',
        className: 'flex w-fit items-center gap-2',
      })}
    >
      <ArrowLeft className="mr-2 h-4 w-4" /> Retour aux profils
    </Link>
  );
};
