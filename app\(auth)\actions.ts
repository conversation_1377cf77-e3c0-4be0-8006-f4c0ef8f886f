'use server';

import SERVER_API from '@/lib/axios.server';
import { AuthService } from '@/services/auth';
import { AxiosError } from 'axios';

export async function sendEmailVerificationCode(email: string) {
  try {
    const auth = new AuthService();
    const user = await auth.getUser();
    if (user.email !== email) {
      const res = await changeEmail(user.email!, email);
      console.log(res);
    }

    const { data } = await SERVER_API.post('/api/otp/sendOTP', {
      email,
      subject: 'Email Verification',
      message: 'Verifier votre addresse mail avec ce code.',
      duration: 1,
    });
    return { error: null, email: data.email };
  } catch (error) {
    if (error instanceof AxiosError) {
      return { error: error.message };
    }
    return { error: 'Unknow Error' };
  }
}

export async function verifyEmailCode(email: string, code: string) {
  try {
    const { data } = await SERVER_API.post('/api/otp/activeEmailAccount', {
      email,
      otp: code,
    });
    return { error: null, user: data };
  } catch (error) {
    if (error instanceof AxiosError) {
      return { error: error.message };
    }
    return { error: 'Unknow Error' };
  }
}

export async function changeEmail(oldEmail: string, newEmail: string) {
  try {
    await SERVER_API.post(`/api/auth/reset-email`, { oldEmail, newEmail });
    return { error: null };
  } catch (error) {
    if (error instanceof AxiosError) {
      return { error: error.message };
    }
  }
}
