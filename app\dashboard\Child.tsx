'use client';
import { columnsF } from '@/components/table/colunm';
import { DataTable } from '@/components/table/filleuls-data-table';
import { AlertDialogCancel } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import API from '@/lib/axios';
import { IUser } from '@/types/next-auth';
import { useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { RefObject, useTransition } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import momo from '@/public/momo.jpg';
import orange from '@/public/orange.jpg';
import { usePath } from '@/hooks/use-path';
type UserNameForm = {
  new: string;
};
type SoldeInput = {
  numero: string;
  country: string;
  amount: string;
  pwd: string;
};
export const UserNameContent = ({
  close,
}: {
  close: RefObject<HTMLButtonElement | null>;
}) => {
  const { update, data: session } = useSession();
  const path = usePath();
  const [isPending, startTransition] = useTransition();
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<UserNameForm>({
    defaultValues: {
      new: '',
    },
  });
  const onSubmit: SubmitHandler<UserNameForm> = async (
    { new: codePromo },
    e,
  ) => {
    e?.preventDefault();
    try {
      const res = await API.patch(
        `api/user/users/${session?.user._id}`,
        {
          codePromo,
        },
        {
          headers: {
            'Current-Path': path,
          },
        },
      );
      console.log(res.data.result);

      startTransition(async () => {
        await update(res.data.result);
        close.current?.click();
      });

      toast({
        variant: 'default',
        description: 'Félicitation! Votre pseudo a bien été mis à jour ',
      });
    } catch (error) {
      if (error instanceof AxiosError) {
        if (
          error.code === 'ERR_NETWORK' ||
          error.code === 'ETIMEDOUT' ||
          error.code === 'ENOTFOUND' ||
          error.code === 'ECONNRESET' ||
          error.code === 'ECONNREFUSED'
        ) {
          return toast({
            variant: 'destructive',
            title: 'Erreur!',
            description: 'Verifiez votre connexion et réessayez!',
          });
        }
      } else {
        toast({
          variant: 'destructive',
          title: 'Erreur',
          description: 'Impossible de mettre votre pseudo à jour!',
        });
      }
    }
  };
  return (
    <div className="flex w-full flex-col space-y-4">
      <h1 className="font-bold">Modifier mon code promo</h1>
      <p className="text-sm font-medium text-gray-500">Code promo=pseudo.</p>
      <form
        onSubmit={handleSubmit(onSubmit)}
        noValidate
        className="grid w-[250px] gap-1"
      >
        <Input
          id="new"
          tabIndex={2}
          placeholder="Nouveau Code"
          type="text"
          autoCapitalize="none"
          disabled={isSubmitting}
          {...register('new', {
            required: {
              value: true,
              message: 'Ce champ est obligatoire',
            },
          })}
        />
        {errors.new && (
          <p className="my-1 text-sm text-red-500">{errors.new.message}</p>
        )}
        <div className="flex gap-1">
          <AlertDialogCancel disabled={isSubmitting}>Annuler</AlertDialogCancel>
          <Button disabled={isSubmitting}>
            {isSubmitting ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              'Valider'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
export const SoldeContent = ({
  close,
}: {
  close: RefObject<HTMLButtonElement | null>;
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<SoldeInput>({
    defaultValues: {
      numero: '',
      amount: '',
      country: 'Cameroun',
      pwd: '',
    },
  });
  const onSubmit: SubmitHandler<SoldeInput> = async ({ numero, pwd }, e) => {
    e?.preventDefault();
    toast({
      variant: 'default',
      description: 'Service indisponible ',
    });
    close?.current?.click();
  };
  return (
    <div className="flex w-full flex-col space-y-4">
      <h1 className="font-bold">Effectuer un retrait de vos commissions</h1>
      <div className="flex gap-2">
        <Image src={momo} width={80} height={80} quality={100} alt="momo" />
        <Image src={orange} width={80} height={80} quality={100} alt="momo" />
      </div>
      <form
        onSubmit={handleSubmit(onSubmit)}
        noValidate
        className="grid w-[250px] gap-1"
      >
        <Input
          id="old"
          tabIndex={1}
          placeholder="Numéro"
          type="text"
          autoCapitalize="none"
          disabled={isSubmitting}
          {...register('numero', {
            required: {
              value: true,
              message: 'Obligatoire!',
            },
          })}
        />
        {errors.numero && (
          <p className="my-1 text-sm text-red-500">{errors.numero.message}</p>
        )}
        <Input
          id="old"
          tabIndex={1}
          placeholder="Montant"
          type="text"
          disabled={isSubmitting}
          {...register('amount', {
            required: {
              value: true,
              message: 'Obligatoire !',
            },
          })}
        />
        {errors.amount && (
          <p className="my-1 text-sm text-red-500">{errors.amount.message}</p>
        )}
        <Input
          id="new"
          tabIndex={2}
          placeholder="Votre mot de passe"
          type="text"
          autoCapitalize="none"
          disabled={isSubmitting}
          {...register('pwd', {
            required: {
              value: true,
              message: 'Champ obligatoire!',
            },
          })}
        />
        {errors.pwd && (
          <p className="my-1 text-sm text-red-500">{errors.pwd.message}</p>
        )}
        <div className="flex gap-1">
          <AlertDialogCancel disabled={isSubmitting}>Annuler</AlertDialogCancel>
          <Button disabled={isSubmitting}>
            {isSubmitting ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              'Valider'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
export const FilleulsContent = () => {
  const { data: session } = useSession();
  const path = usePath();
  const { data, isLoading } = useQuery({
    queryKey: ['getUser'],
    queryFn: async () => {
      const { data } = await API.get<IUser[]>(`/api/user/users/filleuls`, {
        headers: {
          'Current-Path': path,
        },
      });
      // console.log(data.filleuls);
      return data;
    },
    enabled: !!session,
  });

  const rows = data?.map((user) => {
    if (user) {
      const now = new Date();

      const remainDate = user.remains
        ? new Date(
            Math.max(
              ...Object.values(user.remains)
                .filter((remain) => remain?.remain_day)
                .map((remain) => new Date(remain!.remain_day!).getTime()),
            ),
          )
        : null;

      const res = {
        email: user.email,
        last: format(
          new Date(user.lastConnexion ?? user.createdAt),
          "dd/MM/yyyy'-'HH:mm:ss",
        ),
        end:
          remainDate && remainDate > now
            ? format(remainDate, "dd/MM/yyyy'-'HH:mm:ss")
            : '-', // Si aucune date valide, afficher "-"
      };
      return res;
    }
  });

  return (
    <div className="mx-auto flex w-full max-w-[370px] flex-col space-y-4 overflow-x-scroll py-5 md:max-w-full md:overflow-x-hidden">
      <Label>Mes filleuls</Label>
      <div className="mx-auto h-fit w-full overflow-x-scroll md:overflow-x-hidden">
        {isLoading ? (
          <Skeleton className="h-40 min-w-[80vw] md:min-w-[36rem]" />
        ) : (
          <DataTable data={rows ?? []} columns={columnsF as any} />
        )}
      </div>
    </div>
  );
};
