import { getAuthSession } from '@/lib/auth';
import React from 'react';
import { DealerInformation } from '../_components/dealer-information';
import { ProfilesList } from '../_components/profile-list';

import { AddProfile } from '../_components/profile-form';
import DashboardNavigation from '../_components/navigation';
import {
  OralNofification,
  WritingNofification,
} from './correction/_components/notification';

async function ProfilesPage() {
  const session = await getAuthSession();
  const hasDealerSub = session?.user.role == 'dealer';

  return (
    <main className="relative max-w-[100dvw] overflow-hidden">
      <DashboardNavigation />
      <div className="flex flex-col gap-6">
        <DealerInformation user={session?.user!} />
        <div className="flex flex-col gap-6">
          <h2 className="text-xl font-semibold text-gray-800">
            Profils de vos candidats
          </h2>
          <div className="flex items-center gap-4">
            <AddProfile isEnable={hasDealerSub} />
            <OralNofification isEnable={hasDealerSub} />
            <WritingNofification isEnable={hasDealerSub} />
          </div>
          <ProfilesList isEnable={hasDealerSub} />
        </div>
      </div>
    </main>
  );
}

export default ProfilesPage;
