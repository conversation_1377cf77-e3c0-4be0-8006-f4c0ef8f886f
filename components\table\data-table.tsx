'use client';

import * as React from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { DataTablePagination } from './data-table-pagination';
import { useTestState } from '@/context/test';
import { Row } from '@/types';
import { DataTableToolbar } from './data-table-toolbar';

interface BaseTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  dialogRef?: React.RefObject<HTMLDivElement>;
  sortOptions?: SortingState;
}
// ✅ Variante « avec toolbar »
interface TableWithToolbar<TData, TValue>
  extends BaseTableProps<TData, TValue> {
  showToolbar: true; // discriminant
  filteredColumn: string; // obligatoire
}

// ✅ Variante « sans toolbar »
interface TableWithoutToolbar<TData, TValue>
  extends BaseTableProps<TData, TValue> {
  showToolbar?: false | undefined;
  filteredColumn?: string; // optionnel
}
export type DataTableProps<TData, TValue> =
  | TableWithToolbar<TData, TValue>
  | TableWithoutToolbar<TData, TValue>;

export function DataTable<TData, TValue>({
  columns,
  data,
  dialogRef,
  showToolbar,
  filteredColumn,
  sortOptions = [], // default to empty array if not provided
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [sorting, setSorting] = React.useState<SortingState>(sortOptions);
  const { setSelectedRow } = useTestState();

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  return (
    <div className="space-y-4 overflow-x-scroll md:overflow-x-hidden">
      {showToolbar && (
        <DataTableToolbar filteredColumn={filteredColumn} table={table} />
      )}
      <div className="w-full rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  className={`${dialogRef ? 'cursor-pointer' : ''}`}
                  onClick={() => {
                    if (dialogRef?.current) {
                      setSelectedRow(row.original as Row);
                      dialogRef?.current?.click();
                    }
                  }}
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Aucun résultat
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  );
}
