import React, { useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { CommandItem } from './command';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { Country } from '@/types';

interface VirtualizedCountryListProps {
  countries: Country[];
  selectedValue: string;
  onSelect: (value: string) => void;
  height?: number;
}

export const VirtualizedCountryList: React.FC<VirtualizedCountryListProps> = ({
  countries,
  selectedValue,
  onSelect,
  height = 320, // 80 * 4 items par défaut
}) => {
  const parentRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: countries.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 48, // Hauteur estimée de chaque item (12 * 4 = 48px)
    overscan: 5, // Nombre d'éléments à pré-rendre
  });

  if (countries.length === 0) {
    return (
      <div className="flex h-20 items-center justify-center text-sm text-muted-foreground">
        Aucun pays trouvé
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className="overflow-auto"
      style={{
        height: `${height}px`,
      }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const country = countries[virtualItem.index];
          const isSelected =
            selectedValue.toLowerCase() === country.name.common.toLowerCase();

          return (
            <CommandItem
              key={virtualItem.key as any}
              value={country.name.common.toLowerCase()}
              className={cn('absolute left-0 top-0 w-full', {
                'bg-zinc-300': isSelected,
              })}
              style={{
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
              onSelect={(currentValue) => {
                onSelect(
                  currentValue === selectedValue.toLowerCase()
                    ? ''
                    : currentValue.toLowerCase(),
                );
              }}
            >
              <div className="flex items-center justify-between gap-1">
                <Image
                  src={country.flags.svg}
                  height={25}
                  width={25}
                  alt={`Flag of ${country.name.common}`}
                  loading="lazy"
                  className="flex-shrink-0"
                />
                <span className="truncate">
                  {country.name.common.toLowerCase()}
                </span>
              </div>
            </CommandItem>
          );
        })}
      </div>
    </div>
  );
};
